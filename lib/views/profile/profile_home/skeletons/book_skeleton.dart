import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../models/book_case_model.dart';
import '../../../../reusableWidgets/marquee_text.dart';

/// Generic book skeleton widget with configurable height and dynamic content
/// Replaces multiple duplicate skeleton methods with a single reusable component
class BookSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookCaseModel>? bookList;
  final double? height;
  final double? width;
  final Widget? additionalContent;
  final VoidCallback? onTap;

  const BookSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.bookList,
    this.height,
    this.width = 200,
    this.additionalContent,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(left: 10),
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : Colors.transparent,
          border: Border.all(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Book title
              Flexible(
                child: MarqueeList(
                  children: [
                    Text(
                      (bookList != null && index < bookList!.length)
                          ? (bookList![index].bookName ?? 'Unknown Book')
                          : 'Loading...',
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 3),

              // Book author
              Flexible(
                child: MarqueeList(
                  children: [
                    Text(
                      (bookList != null && index < bookList!.length)
                          ? (bookList![index].bookAuthor ?? 'Unknown Author')
                          : 'Loading...',
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              // Additional content (ratings, review button, etc.)
              if (additionalContent != null) ...[
                const Spacer(),
                additionalContent!,
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Currently reading book skeleton
class CurrentReadingSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookCaseModel>? bookList;

  const CurrentReadingSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.bookList,
  });

  @override
  Widget build(BuildContext context) {
    return BookSkeleton(
      isBorder: isBorder,
      index: index,
      bookList: bookList,
      height: 80,
      width: 200,
    );
  }
}

/// To-be-read book skeleton
class ToBeReadSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookCaseModel>? bookList;

  const ToBeReadSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.bookList,
  });

  @override
  Widget build(BuildContext context) {
    return BookSkeleton(
      isBorder: isBorder,
      index: index,
      bookList: bookList,
      height: 80,
      width: 200,
    );
  }
}

/// Completed books skeleton with ratings and review functionality
class CompletedBookSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookCaseModel>? bookList;
  final String? userName;
  final String? userHandler;
  final dynamic userModel;

  const CompletedBookSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.bookList,
    this.userName,
    this.userHandler,
    this.userModel,
  });

  @override
  Widget build(BuildContext context) {
    return BookSkeleton(
      isBorder: isBorder,
      index: index,
      bookList: bookList,
      height: 117,
      width: 200,
      additionalContent: _buildRatingAndReviewSection(context),
    );
  }

  Widget _buildRatingAndReviewSection(BuildContext context) {
    if (bookList == null || index >= bookList!.length) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Review button or rating
        (bookList![index].review?.isNotEmpty ?? false)
            ? GestureDetector(
                onTap: () => _navigateToReview(context),
                child: Text(
                  "Review",
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                  ),
                ),
              )
            : _buildRatingBar(),
        const SizedBox(width: 10),

        // Additional rating if review exists
        if (bookList![index].review?.isNotEmpty ?? false) _buildRatingBar(),
      ],
    );
  }

  Widget _buildRatingBar() {
    if (bookList == null || index >= bookList!.length) {
      return const SizedBox.shrink();
    }

    return Row(
      children: List.generate(5, (starIndex) {
        final rating = bookList![index].ratings ?? 0;
        return Icon(
          starIndex < rating ? Icons.star : Icons.star_border,
          color: AppConstants.textGreenColor,
          size: 20,
        );
      }),
    );
  }

  void _navigateToReview(BuildContext context) {
    // Navigation logic would go here
    // context.pushNamed("book-review", extra: {...});
  }
}
