import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:roundcheckbox/roundcheckbox.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/common_helper.dart';
import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../controller/book_case_controller.dart';
import '../../../controller/profile_controller.dart';
import '../../../models/book_case_model.dart';
import '../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../../reusableWidgets/no_data_widget.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
import '../profile_home/services/profile_sync_service.dart';
// Import shared components
import 'mixins/book_selection_mixin.dart';
import 'models/bookcase_screen_state.dart';
import 'widgets/add_book_popup.dart';
import 'widgets/book_list_item.dart';
import 'widgets/confirmation_dialogs.dart';

class EditCurrentReadingBookScreen extends StatefulWidget {
  final String? buttonName;
  final int? userId;
  final List<BookCaseModel>? topShelfList;
  final List<BookCaseModel>? completedBookList;

  const EditCurrentReadingBookScreen({
    super.key,
    this.buttonName,
    this.userId,
    this.topShelfList,
    this.completedBookList,
  });

  @override
  State<EditCurrentReadingBookScreen> createState() =>
      _EditCurrentReadingBookScreenState();
}

class _EditCurrentReadingBookScreenState
    extends State<EditCurrentReadingBookScreen>
    with BookSelectionMixin<EditCurrentReadingBookScreen> {
  // Core state
  late BookcaseScreenState _screenState;
  List<BookCaseModel>? currentbookCaseList = [];
  List<BookCaseModel> completedBooks = [];
  bool currentReadLoading = false;
  bool isInitialLoading = true;
  bool isPaginationLoading = false;
  int currentReadLimit = 10;
  int currentReadcount = 0;

  // Mark as complete state
  bool isUnknown = false;
  double ratingStar = 1;
  DateTime? selectedDate;
  bool ratingValidation = false;
  bool monthyearValidation = false;
  String? readingCompleteDate;

  // Controllers
  final TextEditingController bookController = TextEditingController();
  final TextEditingController reviewController = TextEditingController();
  final TextEditingController completeDateController = TextEditingController();
  final selectTopShelfController = ValueNotifier<bool>(false);
  final unknownController = ValueNotifier<bool>(false);
  final ScrollController _scrollControllerCurrentRead = ScrollController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Provider controllers
  BookCaseController? bookCaseController;
  ProfileController? profileController;

  // Constants
  final String markAsComplete = BookcaseConstants.markAsComplete;
  final String updateCompletionDate = BookcaseConstants.updateCompletionDate;

  @override
  void initState() {
    super.initState();
    _screenState = BookcaseScreenState();
    _scrollControllerCurrentRead.addListener(_onScrollCurrentRead);

    // Use WidgetsBinding to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
      _initializeData();
    });
  }

  void _initializeControllers() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
  }

  @override
  void dispose() {
    _screenState.dispose();
    _scrollControllerCurrentRead.removeListener(_onScrollCurrentRead);
    _scrollControllerCurrentRead.dispose();
    completeDateController.dispose();
    reviewController.dispose();
    bookController.dispose();
    selectTopShelfController.dispose();
    unknownController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    try {
      // Initialize user ID
      _screenState.loggedInUserId = await CommonHelper.getLoggedInUserId();

      // Load completed books for delete logic
      await _loadCompletedBooks();

      // Load initial current reading books
      await getCurrentReadBookCase(false);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  Future<void> _loadCompletedBooks() async {
    try {
      final responseMap = await Provider.of<BookCaseController>(context,
              listen: false)
          .allBooksRead(widget.userId ?? 0, 10000, 0, false, false, context);

      if (responseMap["statusCode"] == 200) {
        List<BookCaseModel> bookList = [];
        List<dynamic> list = responseMap['data'];

        bookList = (list)
            .map((item) => BookCaseModel.fromJson(item as Map<String, dynamic>))
            .toList();

        var result = CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
        if (result.isNotEmpty) {
          completedBooks = result[2];
        }
      }
    } catch (e) {
      // Silent fail for completed books as it's not critical for main functionality
      debugPrint('Failed to load completed books: $e');
    }
  }

  void _onScrollCurrentRead() {
    if (BookcaseHelper.isAtBottom(_scrollControllerCurrentRead) &&
        BookcaseHelper.canLoadMore(
          currentLength: currentbookCaseList?.length ?? 0,
          totalCount: currentReadcount,
          isLoading: currentReadLoading,
        )) {
      CommonHelper.networkClose(getCurrentReadBookCase(true), context);
    }
  }

  Future<void> getCurrentReadBookCase(bool isMore) async {
    // Prevent multiple simultaneous loads
    if (currentReadLoading) return;

    // Check if we can load more for pagination
    if (isMore &&
        !BookcaseHelper.canLoadMore(
          currentLength: currentbookCaseList?.length ?? 0,
          totalCount: currentReadcount,
          isLoading: currentReadLoading,
        )) {
      return;
    }

    try {
      // Set appropriate loading state
      if (isMore) {
        isPaginationLoading = true;
        currentReadLimit += BookcaseConstants.paginationIncrement;
      } else {
        isInitialLoading = true;
        currentReadLimit = 10; // Reset to initial limit
      }

      currentReadLoading = true;
      if (mounted) setState(() {});

      final responseMap =
          await Provider.of<BookCaseController>(context, listen: false)
              .getCurrentReadBookCase(
                  widget.userId ?? 0, currentReadLimit, 0, context);

      if (!mounted) return;

      if (responseMap["statusCode"] == 200) {
        currentReadcount = responseMap['count'] ?? 0;

        if (responseMap["data"] != null) {
          final bookCaseList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          currentbookCaseList = bookCaseList;
        } else {
          currentbookCaseList = [];
        }
      } else if (responseMap['statusCode'] == 404) {
        currentbookCaseList = [];
        currentReadcount = 0;
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to load books: ${responseMap["message"] ?? "Unknown error"}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    } finally {
      currentReadLoading = false;
      isInitialLoading = false;
      isPaginationLoading = false;
      if (mounted) setState(() {});
    }
  }

  Future<bool> confirmAddBook(bool isMove) async {
    final addBook = BookCaseModel(
      bookId: _screenState.bookId,
      bookAuthor: _screenState.bookAuthor,
      bookName: _screenState.bookName,
      is_currently_reading: true,
    );

    try {
      final result =
          await Provider.of<BookCaseController>(context, listen: false)
              .addBookInBookCase(addBook, context);

      final tempMessage = bookCaseController?.addBookErrorMessage ?? '';
      if (result == 'exist') {
        _screenState.responseMessage = tempMessage;
        _screenState.alreadyExists = true;
        return true;
      } else {
        _screenState.alreadyExists = false;
        // Notify ProfileSyncService about currently reading update
        _notifyCurrentlyReadingUpdate();
        return false;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
      return false;
    }
  }

  String getEditLinkText(BookCaseModel? book) {
    return BookcaseHelper.getEditLinkText(book);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isInitialLoading,
          child: Column(
            children: [
              const SizedBox(height: 25),
              _buildAddBookButton(),
              _buildBooksList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddBookButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          NetworkAwareTap(
            onTap: () => _showAddBookPopup(),
            child: Text(
              BookcaseConstants.addNewBook,
              style: lbItalic.copyWith(
                fontSize: 16,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooksList() {
    // Show skeleton during initial loading
    if (isInitialLoading) {
      return Expanded(
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 25),
          itemCount: 3,
          itemBuilder: (context, index) => BookListItem(
            book: BookCaseModel(
              bookName: 'Loading...',
              bookAuthor: 'Loading...',
            ),
            showSkeleton: true,
            type: BookListItemType.currentReading,
            editText: 'Loading...',
            moveText: 'Loading...',
          ),
        ),
      );
    }

    // Show actual content or empty state
    if (currentbookCaseList?.isNotEmpty ?? false) {
      return Expanded(
        child: ListView.builder(
          controller: _scrollControllerCurrentRead,
          padding: const EdgeInsets.only(bottom: 25),
          cacheExtent: 500, // Preload items for smoother scrolling
          itemCount: isPaginationLoading
              ? (currentbookCaseList?.length ?? 0) + 1
              : (currentbookCaseList?.length ?? 0),
          itemBuilder: (context, index) {
            // Show pagination loader at the end
            if (index == currentbookCaseList?.length && isPaginationLoading) {
              return const Padding(
                padding: EdgeInsets.only(top: 20.0, bottom: 20.0),
                child: Center(
                  child: CircularProgressIndicator(
                    color: AppConstants.primaryColor,
                  ),
                ),
              );
            }

            final book = currentbookCaseList?[index];
            if (book == null) return const SizedBox.shrink();

            return BookListItem(
              book: book,
              type: BookListItemType.currentReading,
              editText: getEditLinkText(book),
              onEdit: () => _showMarkAsCompletePopup(index, book),
              onDelete: () => _showDeleteConfirmation(index, book),
            );
          },
        ),
      );
    } else {
      // Show empty state (no skeleton here)
      return const Expanded(
        child: Padding(
          padding: EdgeInsets.only(top: 25.0),
          child: NoDataWidget(
            message: BookcaseConstants.noCurrentlyReading,
          ),
        ),
      );
    }
  }

  void _showAddBookPopup() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AddBookPopup(
              title: "Add New Book",
              isCurrentlyReading: true,
              loggedInUserId: _screenState.loggedInUserId,
              updateUI: () => setState(() {}),
              onAddBook: confirmAddBook,
              onBookAdded: () => getCurrentReadBookCase(false),
            );
          },
        );
      },
    );
  }

  void _showDeleteConfirmation(int index, BookCaseModel book) {
    ConfirmationDialogs.showDeleteConfirmation(
      context: context,
      title: "Delete Book:",
      message: "Are you sure you want to delete this book?",
      onConfirm: () => _confirmDelete(book.bookId),
    );
  }

  void _showMarkAsCompletePopup(int index, BookCaseModel book) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        reviewController.clear();
        completeDateController.clear();
        ratingValidation = false;
        monthyearValidation = false;
        ratingStar = 0.5;

        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Center(
            child: SingleChildScrollView(
              child: StatefulBuilder(
                builder: (context, setState) {
                  final editText = getEditLinkText(book);
                  return _buildMarkAsCompleteDialog(editText, book, setState);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMarkAsCompleteDialog(
      String editText, BookCaseModel book, StateSetter setState) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: 1.5,
        ),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Form(
          key: _formKey,
          child: Column(
            children: [
              _buildDialogCloseButton(),
              _buildDialogTitle(editText),
              _buildDialogContent(editText, book, setState),
              _buildDialogActions(editText, book, setState),
              const SizedBox(height: 30),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildDialogCloseButton() {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  Widget _buildDialogTitle(String editText) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Column(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Text(
              editText == markAsComplete
                  ? "Mark as Complete"
                  : "Update Completion Date",
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ),
          if (editText == markAsComplete) ...[
            const SizedBox(height: 3),
            Text(
              '(Removes book from "Currently Reading" and Adds to "Books Read")',
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
          const SizedBox(height: 25),
        ],
      ),
    );
  }

  Widget _buildDialogContent(
      String editText, BookCaseModel book, StateSetter setState) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "Book: ${book.bookName}, ${book.bookAuthor}",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(fontSize: 12),
                ),
              ),
            ],
          ),
          const SizedBox(height: 25),
          if (editText == markAsComplete)
            Text(
              "Last Read:",
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          const SizedBox(height: 10),
          _buildDateSelection(setState),
          const SizedBox(height: 15),
          if (editText == markAsComplete) ...[
            _buildRatingSection(setState),
            const SizedBox(height: 15),
            _buildReviewSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildDateSelection(StateSetter setState) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              "Select completion date:",
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 2.8,
              child: TextFormField(
                controller: completeDateController,
                style: lbRegular.copyWith(fontSize: 12),
                decoration: InputDecoration(
                  suffixIcon:
                      const Icon(Icons.calendar_month_outlined, size: 20),
                  contentPadding: const EdgeInsets.all(10),
                  fillColor: const Color.fromRGBO(255, 255, 255, 1),
                  filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: const BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) =>
                    setState(() => monthyearValidation = false),
                onTap: () async {
                  selectedDate = await CommonHelper.getMonthYear(context);
                  setState(() {
                    if (selectedDate != null) {
                      monthyearValidation = false;
                      unknownController.value = false;
                      completeDateController.text =
                          DateFormat('MMM yyyy').format(selectedDate!);
                      readingCompleteDate = completeDateController.text;
                    } else {
                      completeDateController.clear();
                      readingCompleteDate = '';
                    }
                  });
                },
                readOnly: true,
              ),
            ),
            const SizedBox(width: 25),
            _buildUnknownCheckbox(setState),
          ],
        ),
        if (monthyearValidation)
          Positioned(
            top: 55,
            left: 0,
            right: 0,
            child: Text(
              "*Select a date or check 'Unknown'",
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildUnknownCheckbox(StateSetter setState) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "Unknown",
          style: lbRegular.copyWith(fontSize: 12),
        ),
        const SizedBox(width: 10),
        RoundCheckBox(
          isChecked: unknownController.value,
          border: Border.all(color: Colors.transparent),
          onTap: (value) {
            setState(() {
              unknownController.value = value!;
              if (unknownController.value) {
                monthyearValidation = false;
                completeDateController.clear();
              }
              monthyearValidation = false;
            });
          },
          checkedWidget: const Icon(
            Icons.check_circle_outline_rounded,
            color: AppConstants.primaryColor,
          ),
          checkedColor: AppConstants.backgroundColor,
          uncheckedColor: AppConstants.backgroundColor,
          uncheckedWidget: const Icon(
            Icons.circle_outlined,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingSection(StateSetter setState) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "Ratings:",
          style: lbRegular.copyWith(fontSize: 12),
        ),
        const SizedBox(width: 10),
        RatingBar(
          glow: false,
          itemCount: 5,
          itemSize: 25,
          allowHalfRating: true,
          initialRating: ratingStar,
          minRating: 0.5,
          unratedColor: Colors.red,
          ratingWidget: RatingWidget(
            full: const Icon(
              Icons.star,
              color: AppConstants.textGreenColor,
            ),
            half: const Icon(
              Icons.star_half,
              color: AppConstants.textGreenColor,
            ),
            empty: const Icon(
              Icons.star_border_outlined,
              color: AppConstants.textGreenColor,
            ),
          ),
          onRatingUpdate: (double value) {
            setState(() {
              ratingValidation = false;
              ratingStar = value;
            });
          },
        ),
        const SizedBox(width: 5),
        if (ratingValidation)
          Text(
            '*',
            style: lbRegular.copyWith(
              color: AppConstants.redColor,
              fontSize: 20,
            ),
          ),
      ],
    );
  }

  Widget _buildReviewSection() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              "Up to 2000 Character Review (Optional): ",
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: reviewController,
          maxLines: 4,
          maxLength: 2000,
          style: lbRegular.copyWith(fontSize: 12),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(10),
            counterStyle: lbRegular.copyWith(fontSize: 14),
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDialogActions(
      String editText, BookCaseModel book, StateSetter setState) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          NetworkAwareTap(
            onTap: () async {
              bool validation = _formKey.currentState!.validate();

              if (validation) {
                if (completeDateController.text.isEmpty &&
                    !unknownController.value) {
                  setState(() => monthyearValidation = true);
                } else if (editText == markAsComplete && ratingStar == 0) {
                  setState(() => ratingValidation = true);
                } else {
                  final navigator = Navigator.of(context);
                  await _confirmMarkAsComplete(
                      book.bookCaseId, book.bookId, editText);
                  if (mounted) {
                    navigator.pop();
                  }
                }
              }
            },
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 3,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.textGreenColor,
              ),
              child: Center(
                child: Text(
                  editText == markAsComplete ? "Add" : "Update",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
          NetworkAwareTap(
            onTap: () {
              reviewController.clear();
              completeDateController.clear();
              context.pop();
            },
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 3,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.backgroundColor,
                border: Border.all(color: AppConstants.primaryColor),
              ),
              child: Center(
                child: Text(
                  "Cancel",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmMarkAsComplete(
      int? bookCaseId, int? bookId, String editText) async {
    try {
      String? date;
      if (selectedDate != null) {
        date = DateFormat("dd-MM-yyyy").format(selectedDate!);
      }

      BookCaseModel? bookToMarkComplete = currentbookCaseList?.firstWhere(
        (book) => book.bookId == bookId,
        orElse: () => BookCaseModel(),
      );

      if (bookToMarkComplete == null) return;

      BookCaseModel updatedBook = BookCaseModel(
        bookCaseId: bookToMarkComplete.bookCaseId,
        userId: bookToMarkComplete.userId,
        bookId: bookToMarkComplete.bookId,
        bookName: bookToMarkComplete.bookName,
        bookAuthor: bookToMarkComplete.bookAuthor,
        is_currently_reading: false,
        reading_complete_date_String: date,
        ratings: bookToMarkComplete.ratings,
        topShelf: bookToMarkComplete.topShelf,
        review: bookToMarkComplete.review,
        reRead: bookToMarkComplete.reRead,
      );

      if (editText == markAsComplete) {
        updatedBook = BookCaseModel(
          bookCaseId: bookToMarkComplete.bookCaseId,
          userId: bookToMarkComplete.userId,
          bookId: bookToMarkComplete.bookId,
          bookName: bookToMarkComplete.bookName,
          bookAuthor: bookToMarkComplete.bookAuthor,
          is_currently_reading: false,
          reading_complete_date_String: date,
          ratings: ratingStar,
          topShelf: selectTopShelfController.value,
          review: reviewController.text,
          reRead: bookToMarkComplete.reRead,
        );
      }

      await Provider.of<BookCaseController>(context, listen: false)
          .updateBookCase(updatedBook, context);

      await getCurrentReadBookCase(false);

      // Notify ProfileSyncService about bookcase changes
      _notifyBookcaseUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  Future<void> _confirmDelete(int? bookId) async {
    try {
      bool bookExistsInBookCase = false;
      if (completedBooks.isNotEmpty) {
        bookExistsInBookCase =
            completedBooks.any((book) => book.bookId == bookId);
      }

      if (bookExistsInBookCase) {
        final bookToRemoveFromCurrReading = currentbookCaseList?.firstWhere(
          (book) => book.bookId == bookId,
          orElse: () => BookCaseModel(),
        );

        if (bookToRemoveFromCurrReading != null) {
          final updatedBook = BookCaseModel(
            bookCaseId: bookToRemoveFromCurrReading.bookCaseId,
            userId: bookToRemoveFromCurrReading.userId,
            bookId: bookToRemoveFromCurrReading.bookId,
            bookName: bookToRemoveFromCurrReading.bookName,
            bookAuthor: bookToRemoveFromCurrReading.bookAuthor,
            is_currently_reading: false,
            readingCompleteDate:
                bookToRemoveFromCurrReading.readingCompleteDate,
            ratings: bookToRemoveFromCurrReading.ratings,
            topShelf: bookToRemoveFromCurrReading.topShelf,
            review: bookToRemoveFromCurrReading.review,
            reRead: bookToRemoveFromCurrReading.reRead,
          );

          await Provider.of<BookCaseController>(context, listen: false)
              .updateBookCase(updatedBook, context);
        }
      } else {
        await Provider.of<BookCaseController>(context, listen: false)
            .deleteBook(bookId, context);
      }

      await getCurrentReadBookCase(false);

      // Notify ProfileSyncService about currently reading update
      _notifyCurrentlyReadingUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  /// Notify ProfileSyncService about currently reading updates for cross-screen synchronization
  void _notifyCurrentlyReadingUpdate() {
    try {
      ProfileSyncService().onCurrentlyReadingUpdated(currentbookCaseList ?? []);
      log('EditCurrentReadingScreen: Currently reading update notification sent to ProfileSyncService');
    } catch (e) {
      log('EditCurrentReadingScreen: Error notifying currently reading update - $e');
    }
  }

  /// Notify ProfileSyncService about bookcase updates (when books move to completed)
  void _notifyBookcaseUpdate() {
    try {
      // Notify about both currently reading and completed books changes
      ProfileSyncService().onCurrentlyReadingUpdated(currentbookCaseList ?? []);
      ProfileSyncService().onCompletedBooksUpdated(completedBooks);
      log('EditCurrentReadingScreen: Bookcase update notification sent to ProfileSyncService');
    } catch (e) {
      log('EditCurrentReadingScreen: Error notifying bookcase update - $e');
    }
  }
}
