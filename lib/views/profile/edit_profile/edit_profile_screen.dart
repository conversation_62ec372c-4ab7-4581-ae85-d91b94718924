// lib/features/profile/view/edit_profile_screen.dart

import 'dart:developer';
import 'dart:io';

import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/profile_model/edit_profile/update_user_profile_model.dart';
import 'package:eljunto/models/user_model.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:eljunto/views/profile/edit_profile/utils/image_helper.dart';
import 'package:eljunto/views/profile/edit_profile/widgets/club_invitation_selector.dart';
import 'package:eljunto/views/profile/edit_profile/widgets/profile_form_field.dart';
import 'package:eljunto/views/profile/edit_profile/widgets/profile_image_editor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../constants/common_helper.dart';

class EditProfileScreen extends StatefulWidget {
  final String? buttonName;
  final bool updateProfile;
  final UserModel? userModel;

  const EditProfileScreen({
    super.key,
    this.buttonName,
    this.updateProfile = false,
    this.userModel,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  // Controllers and Keys
  final handleController = TextEditingController();
  final nameController = TextEditingController();
  final locationController = TextEditingController();
  final bioController = TextEditingController();

  // State Variables
  UserModel userModel = UserModel();
  bool isDataLoading = true;
  bool isSaveLoading = false;
  int? userId;
  String? logginedMail;
  String userProfilePicture = AppConstants.profileLogoImagePath;
  String? uploadedImageUrl; // To hold the path of the newly uploaded image
  String imageValidationError = '';
  String clubInvitation = "Yes";
  bool get isClubInvitationAllowed => clubInvitation == 'Yes';

  // Validation flags
  bool nameValidation = false;
  bool locationValidation = false;
  bool bioValidation = false;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initializeAndFetchData();
    });
  }

  @override
  void dispose() {
    handleController.dispose();
    nameController.dispose();
    locationController.dispose();
    bioController.dispose();
    super.dispose();
  }

  Future<void> _initializeAndFetchData() async {
    userId = await CommonHelper.getLoggedInUserId();
    logginedMail = await CommonHelper.getLoggedinUserMail();
    await _fetchUserDetails();
  }

  Future<void> _fetchUserDetails() async {
    if (!mounted) return;
    setState(() => isDataLoading = true);
    try {
      final userController =
          Provider.of<UserController>(context, listen: false);
      await userController.getUserDetailsByUserId(userId ?? 0, context);
      userModel = userController.userModel;
      _populateFieldsFromModel();
    } catch (e) {
      log('An error occurred while fetching user details: $e');
      // Optionally show an error message to the user
    } finally {
      if (mounted) setState(() => isDataLoading = false);
    }
  }

  void _populateFieldsFromModel() {
    handleController.text = userModel.data?.userHandle ?? '';
    nameController.text = userModel.data?.userName ?? '';
    locationController.text = userModel.data?.userLocation ?? '';
    bioController.text = userModel.data?.userBio ?? '';
    clubInvitation =
        (userModel.data?.userClubInvitation ?? false) ? "Yes" : "No";
    userProfilePicture = userModel.data?.userProfilePicture?.isNotEmpty ?? false
        ? Config.imageBaseUrl + userModel.data!.userProfilePicture!
        : AppConstants.profileLogoImagePath;
  }

  Future<void> _pickAndUploadImage() async {
    setState(() => isDataLoading = true); // Use the main loader

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      // Basic Validations
      final fileSize = await pickedFile.length();
      if (fileSize > 10 * 1024 * 1024) {
        // 10 MB
        setState(() {
          imageValidationError = 'Image must be under 10 MB.';
          isDataLoading = false;
        });
        return;
      }

      final mimeType = CommonHelper.getMimeType(pickedFile.path);
      if (mimeType != 'image/jpeg' && mimeType != 'image/png') {
        setState(() {
          imageValidationError = 'Please select a JPG or PNG image.';
          isDataLoading = false;
        });
        return;
      }

      final croppedFile = await cropUserImage(pickedFile);
      if (croppedFile != null) {
        final imageFile = File(croppedFile.path);
        // final circularImageFile = await convertSquareImageToCircle(imageFile); // This step may not be necessary if CropStyle.circle is used
        try {
          if (mounted) {
            final responseMap =
                await Provider.of<UserController>(context, listen: false)
                    .uploadFile(imageFile, userId ?? 0, context);
            if (responseMap.containsKey('data')) {
              setState(() {
                uploadedImageUrl = responseMap["data"];
                userProfilePicture = Config.imageBaseUrl + uploadedImageUrl!;
                imageValidationError = '';
              });
            } else {
              throw Exception(responseMap['error'] ?? 'Unknown upload error');
            }
          }
        } catch (e) {
          log('Error uploading image: $e');
          setState(
              () => imageValidationError = 'Upload failed. Please try again.');
        }
      }
    }
    setState(() => isDataLoading = false);
  }

  Future<void> _saveProfile() async {
    FocusScope.of(context).unfocus();

    // Manual validation check
    setState(() {
      nameValidation = nameController.text.isEmpty;
      locationValidation = locationController.text.isEmpty;
      bioValidation = bioController.text.isEmpty;
    });

    if (nameValidation || locationValidation || bioValidation) {
      return;
    }

    setState(() => isSaveLoading = true);

    var userData = UserProfileUpdateModel(
      userId: userId,
      userEmailId: logginedMail,
      userProfilePicture: uploadedImageUrl,
      userName: nameController.text,
      userLocation: locationController.text,
      userBio: bioController.text,
      userClubInvitation: isClubInvitationAllowed,
    );

    try {
      final success =
          await Provider.of<ProfileController>(context, listen: false)
              .updateProfileFunction(userData, context);

      if (success && mounted) {
        await _saveToPrefs();
        context.pop(true);
      }
    } catch (e) {
      log("Error saving profile: $e");
    } finally {
      if (mounted) setState(() => isSaveLoading = false);
    }
  }

  Future<void> _saveToPrefs() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    await pref.setString('userName', nameController.text);
    await pref.setString('userLocation', locationController.text);
    await pref.setString('userBio', bioController.text);
    await pref.setBool('isUserBioAndLocationAvailable', true);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            fit: BoxFit.fill,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(80),
            child: Container(
              decoration: const BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.5, color: AppConstants.primaryColor)),
              ),
              child: PreviousScreenAppBar(
                bookName: widget.buttonName,
                isSetProfile: widget.updateProfile,
              ),
            ),
          ),
          body: Skeletonizer(
            enabled: isDataLoading,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const SizedBox(height: 25),
                          ProfileImageEditor(
                            imageUrl: userProfilePicture,
                            onTap: _pickAndUploadImage,
                            validationMessage: imageValidationError,
                          ),
                          const SizedBox(height: 25),
                          ProfileFormField(
                            label: 'Handle:',
                            controller: handleController,
                            isReadOnly: true,
                            isEnabled: false,
                          ),
                          const SizedBox(height: 25),
                          ProfileFormField(
                            label: 'Name:',
                            controller: nameController,
                            textCapitalization: TextCapitalization.sentences,
                            onChanged: (_) =>
                                setState(() => nameValidation = false),
                            validationMessage:
                                nameValidation ? '*Enter name' : null,
                          ),
                          const SizedBox(height: 25),
                          ProfileFormField(
                            label: 'Location:',
                            controller: locationController,
                            maxLength: 30,
                            textCapitalization: TextCapitalization.sentences,
                            onChanged: (_) =>
                                setState(() => locationValidation = false),
                            validationMessage:
                                locationValidation ? '*Enter location' : null,
                          ),
                          const SizedBox(height: 25),
                          ProfileFormField(
                            label: 'Bio (150 Characters max):',
                            controller: bioController,
                            maxLength: 150,
                            textCapitalization: TextCapitalization.sentences,
                            onChanged: (_) =>
                                setState(() => bioValidation = false),
                            validationMessage:
                                bioValidation ? '*Enter bio' : null,
                          ),
                          const SizedBox(height: 25),
                          ClubInvitationSelector(
                            groupValue: clubInvitation,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() => clubInvitation = value);
                              }
                            },
                          ),
                          const SizedBox(height: 40),
                          CustomLoaderButton(
                            buttonWidth: isSaveLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width,
                            buttonRadius: 30.0,
                            buttonChild: isSaveLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    'Save',
                                    style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor),
                                  ),
                            buttonPressed: _saveProfile,
                          ),
                          const SizedBox(height: 25),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
