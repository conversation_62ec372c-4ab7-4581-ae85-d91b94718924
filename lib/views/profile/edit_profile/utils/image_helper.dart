// lib/features/profile/utils/image_helper.dart

import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:eljunto/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

/// Creates a circular version of the given image.
Future<File> convertSquareImageToCircle(File imageFile) async {
  final image = await decodeImageFromList(await imageFile.readAsBytes());
  final output = await _createCircleImage(image);
  await imageFile.writeAsBytes(output);
  return imageFile;
}

/// Helper function to perform the canvas drawing for the circular image.
Future<Uint8List> _createCircleImage(ui.Image image) async {
  final pictureRecorder = ui.PictureRecorder();
  final canvas = Canvas(pictureRecorder);
  final size = image.width.toDouble();
  final radius = size / 2;

  final paint = Paint()..isAntiAlias = true;

  // Clip the canvas to a circle
  canvas.clipPath(
    Path()
      ..addOval(
        Rect.fromCircle(center: Offset(radius, radius), radius: radius),
      ),
  );

  // Draw the image onto the clipped canvas
  canvas.drawImage(image, Offset.zero, paint);

  final picture = pictureRecorder.endRecording();
  final img = await picture.toImage(size.toInt(), size.toInt());
  final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
  return byteData!.buffer.asUint8List();
}

/// Opens the image cropper UI for the user to crop the picked image.
Future<CroppedFile?> cropUserImage(XFile pickedFile) async {
  final croppedImage = await ImageCropper().cropImage(
    sourcePath: pickedFile.path,
    aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
    compressFormat: ImageCompressFormat.png,
    compressQuality: 100,
    uiSettings: [
      AndroidUiSettings(
        toolbarTitle: 'Crop your image',
        toolbarColor: AppConstants.primaryColor,
        cropFrameColor: AppConstants.primaryColor,
        toolbarWidgetColor: Colors.white,
        cropStyle: CropStyle.circle,
        hideBottomControls: true,
        lockAspectRatio: true,
        showCropGrid: true,
      ),
      IOSUiSettings(
        minimumAspectRatio: 1,
        title: 'Crop your image',
        doneButtonTitle: 'Save',
        cancelButtonTitle: 'Cancel',
        cropStyle: CropStyle.circle,
        showCancelConfirmationDialog: true,
        hidesNavigationBar: false,
        rotateButtonsHidden: true,
        rotateClockwiseButtonHidden: true,
        aspectRatioPickerButtonHidden: true,
        aspectRatioLockEnabled: true,
      ),
    ],
  );
  return croppedImage;
}
